import { Organization, InstallationStatus } from '@/src/lib/database/models';
import { errorHandlingService, createErrorContext } from './error-handling';
import {
  GitHubPullRequestPayload,
  GitHubInstallationPayload,
  WebhookSyncResult,
  PullRequestPayloadSchema
} from './webhook-types';
import { RateLimiter } from '../utils/rate-limiter';
import { ExternalAPIService } from './external-api';
import { InstallationHandlers } from './installation-handlers';
import { RepositoryHandlers } from './repository-handlers';
import { GitHubStatusService } from './github-status';

/**
 * Webhook-driven synchronization service
 * Handles real-time updates from GitHub webhooks
 */
export class WebhookSyncService {
  private rateLimiter = new RateLimiter();
  private externalAPI = new ExternalAPIService();
  private installationHandlers = new InstallationHandlers();
  private repositoryHandlers = new RepositoryHandlers();
  private githubStatusService = new GitHubStatusService();

  // Track recently processed PRs to prevent infinite loops
  private recentlyProcessedPRs = new Map<string, number>();
  private readonly PR_PROCESSING_COOLDOWN = 5 * 60 * 1000; // 5 minutes

  /**
   * Record metrics for monitoring
   */
  private recordMetrics(action: string, success: boolean, duration: number): void {
    const metrics = {
      action,
      success,
      duration,
      timestamp: new Date().toISOString()
    };

    // In production, send to monitoring service (e.g., DataDog, New Relic)
    console.log(`Webhook Metrics:`, metrics);
  }

  /**
   * Check if a PR was recently processed to prevent infinite loops
   */
  private isPRRecentlyProcessed(repoId: string, prNumber: number): boolean {
    const key = `${repoId}:${prNumber}`;
    const lastProcessed = this.recentlyProcessedPRs.get(key);

    if (!lastProcessed) {
      return false;
    }

    const now = Date.now();
    const timeSinceProcessed = now - lastProcessed;

    // Clean up old entries
    if (timeSinceProcessed > this.PR_PROCESSING_COOLDOWN) {
      this.recentlyProcessedPRs.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Mark a PR as recently processed
   */
  private markPRAsProcessed(repoId: string, prNumber: number): void {
    const key = `${repoId}:${prNumber}`;
    this.recentlyProcessedPRs.set(key, Date.now());

    // Clean up old entries periodically
    if (this.recentlyProcessedPRs.size > 1000) {
      const now = Date.now();
      for (const [k, timestamp] of this.recentlyProcessedPRs.entries()) {
        if (now - timestamp > this.PR_PROCESSING_COOLDOWN) {
          this.recentlyProcessedPRs.delete(k);
        }
      }
    }
  }

  /**
   * Map pull request data with optional extended fields
   */
  private mapPullRequestData(payload: GitHubPullRequestPayload, includeExtended = false) {
    const baseData = {
      id: payload.pull_request.id,
      number: payload.pull_request.number,
      title: payload.pull_request.title,
      state: payload.pull_request.state,
      action: payload.action,
      repository: payload.repository.full_name,
      author: payload.pull_request.user.login,
      sender: payload.sender.login,
    };

    if (includeExtended) {
      return {
        ...baseData,
        url: payload.pull_request.html_url,
        base_branch: payload.pull_request.base.ref,
        head_branch: payload.pull_request.head.ref,
        draft: payload.pull_request.draft,
        mergeable: payload.pull_request.mergeable,
        created_at: payload.pull_request.created_at,
        updated_at: payload.pull_request.updated_at,
        diff_url: payload.pull_request.diff_url,
        patch_url: payload.pull_request.patch_url,
        commits: payload.pull_request.commits,
        additions: payload.pull_request.additions,
        deletions: payload.pull_request.deletions,
        changed_files: payload.pull_request.changed_files,
        commits_url: payload.pull_request.commits_url,
        review_comments_url: payload.pull_request.review_comments_url,
        statuses_url: payload.pull_request.statuses_url
      };
    }

    return baseData;
  }

  /**
   * Handle installation webhook events
   */
  async handleInstallationEvent(payload: any): Promise<WebhookSyncResult> {
    const startTime = Date.now();
    const { action, installation, repositories, repository_selection } = payload;
    const installationId = installation?.id?.toString();

    const result: WebhookSyncResult = {
      success: false,
      action: `installation.${action || 'unknown'}`,
      repositoriesAffected: 0,
      errors: []
    };

    try {
      // Basic validation
      if (!installationId) {
        result.errors.push('Missing installation ID in payload');
        this.recordMetrics(action || 'unknown', false, Date.now() - startTime);
        return result;
      }

      // Rate limiting check
      const rateLimitKey = `installation_${installationId}`;
      if (!this.rateLimiter.isAllowed(rateLimitKey, 5, 300000)) { // 5 requests per 5 minutes
        result.success = false;
        result.errors.push('Rate limit exceeded for installation webhooks');
        this.recordMetrics(action, false, Date.now() - startTime);
        return result;
      }
      switch (action) {
        case 'created':
          await this.installationHandlers.handleInstallationCreated(installationId, installation, repositories, repository_selection, result);
          break;

        case 'deleted':
          await this.installationHandlers.handleInstallationDeleted(installationId, installation, result);
          break;

        case 'suspend':
          await this.installationHandlers.handleInstallationSuspended(installationId, installation, result);
          break;

        case 'unsuspend':
          await this.installationHandlers.handleInstallationUnsuspended(installationId, installation, result);
          break;

        default:
          result.errors.push(`Unhandled installation action: ${action}`);
      }

      const duration = Date.now() - startTime;
      result.metrics = {
        duration,
        timestamp: new Date()
      };

      console.log(`Installation webhook processed:`, {
        action,
        installationId,
        repositorySelection: repository_selection,
        repositoriesAffected: result.repositoriesAffected,
        success: result.success,
        errors: result.errors,
        duration
      });

      this.recordMetrics(action, result.success, duration);
      return result;

    } catch (error) {
      const duration = Date.now() - startTime;

      // Use error handling service
      const context = createErrorContext(
        undefined, // userId
        undefined, // organizationId
        installationId,
        undefined, // repositoryId
        'installation_webhook'
      );

      const platyfendError = errorHandlingService.handleWebhookError(error, context);
      errorHandlingService.logError(platyfendError);

      result.errors.push(platyfendError.message);
      result.metrics = {
        duration,
        timestamp: new Date()
      };

      this.recordMetrics(action || 'unknown', false, duration);
      return result;
    }
  }

  /**
   * Handle installation repositories webhook events
   */
  async handleInstallationRepositoriesEvent(payload: any): Promise<WebhookSyncResult> {
    const startTime = Date.now();
    const { action, installation, repositories_added, repositories_removed } = payload;
    const installationId = installation?.id?.toString();

    const result: WebhookSyncResult = {
      success: false,
      action: `installation_repositories.${action || 'unknown'}`,
      repositoriesAffected: 0,
      errors: []
    };

    try {
      // Basic validation
      if (!installationId) {
        result.errors.push('Missing installation ID in payload');
        this.recordMetrics(action || 'unknown', false, Date.now() - startTime);
        return result;
      }

      // Rate limiting check
      const rateLimitKey = `installation_repos_${installationId}`;
      if (!this.rateLimiter.isAllowed(rateLimitKey, 10, 300000)) { // 10 requests per 5 minutes
        result.success = false;
        result.errors.push('Rate limit exceeded for installation repositories webhooks');
        this.recordMetrics(action, false, Date.now() - startTime);
        return result;
      }
      // Find organization by installation ID
      const organization = await Organization.findOne({
        installation_id: installationId,
        installation_status: InstallationStatus.ACTIVE
      });

      if (!organization) {
        result.errors.push(`No active organization found for installation ${installationId}`);
        return result;
      }

      result.organizationId = organization._id.toString();

      switch (action) {
        case 'added':
          if (repositories_added && repositories_added.length > 0) {
            await this.installationHandlers.handleRepositoriesAdded(organization, repositories_added, installationId, result);
          }
          break;

        case 'removed':
          if (repositories_removed && repositories_removed.length > 0) {
            await this.installationHandlers.handleRepositoriesRemoved(organization, repositories_removed, result);
          }
          break;

        default:
          result.errors.push(`Unhandled installation repositories action: ${action}`);
      }

      const duration = Date.now() - startTime;
      result.metrics = {
        duration,
        timestamp: new Date()
      };

      console.log(`Installation repositories webhook processed:`, {
        action,
        installationId,
        organizationId: result.organizationId,
        repositoriesAffected: result.repositoriesAffected,
        success: result.success,
        errors: result.errors,
        duration
      });

      this.recordMetrics(action, result.success, duration);
      return result;

    } catch (error) {
      const duration = Date.now() - startTime;

      // Use error handling service
      const context = createErrorContext(
        undefined, // userId
        result.organizationId,
        installationId,
        undefined, // repositoryId
        'installation_repositories_webhook'
      );

      const platyfendError = errorHandlingService.handleWebhookError(error, context);
      errorHandlingService.logError(platyfendError);

      result.errors.push(platyfendError.message);
      result.metrics = {
        duration,
        timestamp: new Date()
      };

      this.recordMetrics(action || 'unknown', false, duration);
      return result;
    }
  }

  /**
   * Handle pull request webhook events
   */
  async handlePullRequestEvent(payload: any): Promise<WebhookSyncResult> {
    const startTime = Date.now();

    const result: WebhookSyncResult = {
      success: true,
      action: `pull_request.${payload.action || 'unknown'}`,
      repositoriesAffected: 0,
      errors: []
    };

    try {
      // Rate limiting check
      const rateLimitKey = `pr_${payload.repository?.id || 'unknown'}`;
      if (!this.rateLimiter.isAllowed(rateLimitKey)) {
        result.success = false;
        result.errors.push('Rate limit exceeded for pull request webhooks');
        this.recordMetrics(payload.action, false, Date.now() - startTime);
        return result;
      }

      // Validate payload structure
      const validationResult = PullRequestPayloadSchema.safeParse(payload);
      if (!validationResult.success) {
        result.success = false;
        result.errors.push(`Invalid payload structure: ${validationResult.error.message}`);
        this.recordMetrics(payload.action, false, Date.now() - startTime);
        return result;
      }

      const validatedPayload = validationResult.data;
      result.action = `pull_request.${validatedPayload.action}`;

      // Handle opened, reopened, and synchronize actions with extended data
      if (validatedPayload.action === 'opened' || validatedPayload.action === 'reopened' || validatedPayload.action === 'synchronize') {
        const repoId = validatedPayload.repository.id.toString();
        const prNumber = validatedPayload.pull_request.number;

        // For synchronize events, use a shorter cooldown (1 minute) since new commits should trigger analysis
        // For opened/reopened, use the full cooldown (5 minutes) to prevent immediate re-processing
        const isRecentlyProcessed = validatedPayload.action === 'synchronize'
          ? this.isPRRecentlyProcessed(repoId, prNumber) && (Date.now() - (this.recentlyProcessedPRs.get(`${repoId}:${prNumber}`) || 0)) < 60000
          : this.isPRRecentlyProcessed(repoId, prNumber);

        // if (isRecentlyProcessed) {
        //   console.log(`PR #${prNumber} in repo ${repoId} was recently processed, skipping to prevent infinite loop`);
        //   result.action = `pull_request.${validatedPayload.action}_skipped`;
        //   this.recordMetrics(validatedPayload.action, true, Date.now() - startTime);
        //   return result;
        // }

        // Mark this PR as being processed
        this.markPRAsProcessed(repoId, prNumber);
        const prData = this.mapPullRequestData(validatedPayload, true);
        console.log(`Pull Request ${validatedPayload.action}:`, prData);

        // Create GitHub status check to show "running" status
        try {
          const owner = validatedPayload.repository.owner.login;
          const repo = validatedPayload.repository.name;
          const sha = validatedPayload.pull_request.head.sha;
          const prNumber = validatedPayload.pull_request.number;

          // Find installation ID for this repository
          const installationId = await this.githubStatusService.getInstallationIdForRepo(owner, repo);

          if (installationId) {
            await this.githubStatusService.setPendingStatus(
              installationId,
              owner,
              repo,
              sha,
              prNumber
            );
            console.log(`GitHub status set to pending for PR #${prNumber} in ${owner}/${repo}`);
          } else {
            console.warn(`No installation found for ${owner}/${repo}, skipping status check`);
          }
        } catch (statusError) {
          console.error('Error setting GitHub status:', statusError);
          // Don't fail webhook processing if status creation fails
        }

        // Send data to external API with retry logic
        try {
          const apiSuccess = await this.externalAPI.sendPullRequestDataWithRetry(prData);
          if (!apiSuccess) {
            console.warn(`Failed to send PR ${validatedPayload.action} data to external API after retries`);
            // Don't fail the webhook processing if external API fails
          }
        } catch (apiError) {
          console.error('Unexpected error in external API call:', apiError);
          // Log error but don't fail webhook processing
        }
      } else {
        // Handle other actions with basic data
        const prData = this.mapPullRequestData(validatedPayload, false);
        console.log(`Pull Request ${validatedPayload.action}:`, prData);
      }

      const duration = Date.now() - startTime;
      result.metrics = {
        duration,
        timestamp: new Date()
      };

      this.recordMetrics(validatedPayload.action, true, duration);
      return result;

    } catch (error) {
      const duration = Date.now() - startTime;

      // Use error handling service for structured error handling
      const context = createErrorContext(
        undefined, // userId
        undefined, // organizationId
        undefined, // installationId
        payload.repository?.id?.toString(),
        'pull_request_webhook'
      );

      const platyfendError = errorHandlingService.handleWebhookError(error, context);
      errorHandlingService.logError(platyfendError);

      result.success = false;
      result.errors.push(platyfendError.message);
      result.metrics = {
        duration,
        timestamp: new Date()
      };

      this.recordMetrics(payload.action || 'unknown', false, duration);
      return result;
    }
  }

  /**
   * Handle repository webhook events
   */
  async handleRepositoryEvent(payload: any): Promise<WebhookSyncResult> {
    const startTime = Date.now();
    const { action, repository } = payload;
    const repoId = repository?.id?.toString();

    const result: WebhookSyncResult = {
      success: false,
      action: `repository.${action || 'unknown'}`,
      repositoriesAffected: 0,
      errors: []
    };

    try {
      // Basic validation
      if (!repoId) {
        result.errors.push('Missing repository ID in payload');
        this.recordMetrics(action || 'unknown', false, Date.now() - startTime);
        return result;
      }

      // Rate limiting check
      const rateLimitKey = `repository_${repoId}`;
      if (!this.rateLimiter.isAllowed(rateLimitKey, 15, 300000)) { // 15 requests per 5 minutes
        result.success = false;
        result.errors.push('Rate limit exceeded for repository webhooks');
        this.recordMetrics(action, false, Date.now() - startTime);
        return result;
      }
      // Find organizations that have this repository
      const organizations = await Organization.find({
        'repos.repo_id': repoId
      });

      if (organizations.length === 0) {
        console.log(`No organizations found with repository ${repoId}`);
        result.success = true;
        return result;
      }

      for (const organization of organizations) {
        try {
          switch (action) {
            case 'renamed':
              await this.repositoryHandlers.handleRepositoryRenamed(organization, repoId, repository, result);
              break;

            case 'transferred':
              await this.repositoryHandlers.handleRepositoryTransferred(organization, repoId, repository, result);
              break;

            case 'privatized':
            case 'publicized':
              await this.repositoryHandlers.handleRepositoryVisibilityChanged(organization, repoId, repository, result);
              break;

            case 'deleted':
              await this.repositoryHandlers.handleRepositoryDeleted(organization, repoId, result);
              break;

            default:
              console.log(`Unhandled repository action: ${action} for repo ${repoId}`);
          }
        } catch (error) {
          result.errors.push(`Failed to update repository ${repoId} in organization ${organization._id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      result.success = result.errors.length === 0;
      const duration = Date.now() - startTime;
      result.metrics = {
        duration,
        timestamp: new Date()
      };

      console.log(`Repository webhook processed:`, {
        action,
        repoId,
        organizationsAffected: organizations.length,
        repositoriesAffected: result.repositoriesAffected,
        success: result.success,
        errors: result.errors,
        duration
      });

      this.recordMetrics(action, result.success, duration);
      return result;

    } catch (error) {
      const duration = Date.now() - startTime;

      // Use error handling service
      const context = createErrorContext(
        undefined, // userId
        undefined, // organizationId
        undefined, // installationId
        repoId,
        'repository_webhook'
      );

      const platyfendError = errorHandlingService.handleWebhookError(error, context);
      errorHandlingService.logError(platyfendError);

      result.errors.push(platyfendError.message);
      result.metrics = {
        duration,
        timestamp: new Date()
      };

      this.recordMetrics(action || 'unknown', false, duration);
      return result;
    }
  }
}

// Singleton instance
export const webhookSyncService = new WebhookSyncService();

// Helper functions for webhook handlers
export async function handleInstallationWebhook(payload: any): Promise<WebhookSyncResult> {
  return webhookSyncService.handleInstallationEvent(payload);
}

export async function handleInstallationRepositoriesWebhook(payload: any): Promise<WebhookSyncResult> {
  return webhookSyncService.handleInstallationRepositoriesEvent(payload);
}

export async function handleRepositoryWebhook(payload: any): Promise<WebhookSyncResult> {
  return webhookSyncService.handleRepositoryEvent(payload);
}

export async function handlePullRequestWebhook(payload: any): Promise<WebhookSyncResult> {
  return webhookSyncService.handlePullRequestEvent(payload);
}
